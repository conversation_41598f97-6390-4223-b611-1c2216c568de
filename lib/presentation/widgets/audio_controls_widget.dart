import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../providers/app_state_provider.dart';

/// Widget for audio playback controls and settings
class AudioControlsWidget extends StatefulWidget {
  const AudioControlsWidget({Key? key}) : super(key: key);

  @override
  State<AudioControlsWidget> createState() => _AudioControlsWidgetState();
}

class _AudioControlsWidgetState extends State<AudioControlsWidget> {
  final TextEditingController _urlController = TextEditingController();
  double _volume = 1.0;
  double _speed = 1.0;

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Audio Source Section
              _buildAudioSourceCard(context, appState),
              const SizedBox(height: 16),

              // Playback Controls Section
              _buildPlaybackControlsCard(context, appState),
              const SizedBox(height: 16),

              // Audio Settings Section
              _buildAudioSettingsCard(context, appState),
              const SizedBox(height: 16),

              // Audio Statistics Section
              _buildAudioStatisticsCard(context, appState),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAudioSourceCard(
    BuildContext context,
    AppStateProvider appState,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Audio Source',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // URL Input
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'Audio URL',
                hintText: 'Enter audio URL or select from presets',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.link),
              ),
              onSubmitted: (url) {
                if (url.isNotEmpty) {
                  appState.loadAndPlayAudio(url);
                }
              },
            ),
            const SizedBox(height: 12),

            // Preset URLs
            Wrap(
              spacing: 8.0,
              children:
                  AppConstants.sampleAudioUrls.map((url) {
                    final fileName = url.split('/').last;
                    return ActionChip(
                      label: Text(fileName),
                      onPressed: () {
                        _urlController.text = url;
                        appState.loadAndPlayAudio(url);
                      },
                    );
                  }).toList(),
            ),
            const SizedBox(height: 12),

            // Load Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    _urlController.text.isEmpty
                        ? null
                        : () {
                          appState.loadAndPlayAudio(_urlController.text);
                        },
                icon: const Icon(Icons.download),
                label: const Text('Load Audio'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaybackControlsCard(
    BuildContext context,
    AppStateProvider appState,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Playback Controls',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // Audio State Indicator
            Row(
              children: [
                Icon(
                  _getAudioStateIcon(appState.audioState),
                  color: _getAudioStateColor(appState.audioState),
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getAudioStateDisplayName(appState.audioState),
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      if (appState.audioDuration != null)
                        Text(
                          '${_formatDuration(appState.audioPosition)} / ${_formatDuration(appState.audioDuration!)}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress Bar
            if (appState.audioDuration != null) ...[
              LinearProgressIndicator(
                value:
                    appState.audioDuration!.inMilliseconds > 0
                        ? appState.audioPosition.inMilliseconds /
                            appState.audioDuration!.inMilliseconds
                        : 0.0,
                backgroundColor: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
            ],

            // Control Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed:
                      appState.isAudioReady
                          ? () {
                            // Seek backward 10 seconds
                            // TODO: Implement seek functionality
                            // final newPosition = Duration(
                            //   milliseconds: (appState.audioPosition.inMilliseconds - 10000)
                            //     .clamp(0, appState.audioDuration?.inMilliseconds ?? 0),
                            // );
                            // appState.seekAudio(newPosition);
                          }
                          : null,
                  icon: const Icon(Icons.replay_10),
                  iconSize: 32,
                ),

                IconButton(
                  onPressed:
                      appState.isAudioReady
                          ? () {
                            if (appState.isAudioPlaying) {
                              appState.pauseAudio();
                            } else {
                              if (_urlController.text.isNotEmpty) {
                                appState.loadAndPlayAudio(_urlController.text);
                              }
                            }
                          }
                          : null,
                  icon: Icon(
                    appState.isAudioPlaying ? Icons.pause : Icons.play_arrow,
                  ),
                  iconSize: 48,
                  style: IconButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),

                IconButton(
                  onPressed:
                      appState.isAudioReady
                          ? () {
                            appState.stopAudio();
                          }
                          : null,
                  icon: const Icon(Icons.stop),
                  iconSize: 32,
                ),

                IconButton(
                  onPressed:
                      appState.isAudioReady
                          ? () {
                            // Seek forward 10 seconds
                            // TODO: Implement seek functionality
                            // final newPosition = Duration(
                            //   milliseconds: (appState.audioPosition.inMilliseconds + 10000)
                            //     .clamp(0, appState.audioDuration?.inMilliseconds ?? 0),
                            // );
                            // appState.seekAudio(newPosition);
                          }
                          : null,
                  icon: const Icon(Icons.forward_10),
                  iconSize: 32,
                ),
              ],
            ),

            // Sync Info for Slaves
            if (appState.deviceRole == DeviceRole.slave &&
                appState.currentTimeOffset != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.sync_alt, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Synchronized playback with ${appState.currentTimeOffset}μs offset',
                        style: TextStyle(color: Colors.blue.shade700),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAudioSettingsCard(
    BuildContext context,
    AppStateProvider appState,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Audio Settings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // Volume Control
            Row(
              children: [
                const Icon(Icons.volume_down),
                Expanded(
                  child: Slider(
                    value: _volume,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20,
                    label: '${(_volume * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _volume = value;
                      });
                      // appState.setVolume(value); // Would need to implement this
                    },
                  ),
                ),
                const Icon(Icons.volume_up),
              ],
            ),

            // Speed Control
            Row(
              children: [
                const Icon(Icons.slow_motion_video),
                Expanded(
                  child: Slider(
                    value: _speed,
                    min: 0.5,
                    max: 2.0,
                    divisions: 15,
                    label: '${_speed.toStringAsFixed(1)}x',
                    onChanged: (value) {
                      setState(() {
                        _speed = value;
                      });
                      // appState.setSpeed(value); // Would need to implement this
                    },
                  ),
                ),
                const Icon(Icons.fast_forward),
              ],
            ),

            // Audio Format Info
            if (appState.isAudioReady) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Audio Information',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Duration: ${_formatDuration(appState.audioDuration!)}',
                    ),
                    Text(
                      'Current Position: ${_formatDuration(appState.audioPosition)}',
                    ),
                    Text('Volume: ${(_volume * 100).round()}%'),
                    Text('Speed: ${_speed.toStringAsFixed(1)}x'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAudioStatisticsCard(
    BuildContext context,
    AppStateProvider appState,
  ) {
    final stats = appState.getAudioStatistics();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Audio Statistics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            ...stats.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(_formatStatKey(entry.key)),
                    Text(
                      _formatStatValue(entry.key, entry.value),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatStatKey(String key) {
    return key
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _formatStatValue(String key, dynamic value) {
    if (value == null) return 'N/A';

    switch (key) {
      case 'position':
      case 'duration':
      case 'bufferedPosition':
        return _formatDuration(Duration(milliseconds: value as int));
      case 'isScheduled':
        return (value as bool) ? 'Yes' : 'No';
      case 'scheduledTime':
        return value != null
            ? DateTime.fromMicrosecondsSinceEpoch(value as int).toString()
            : 'N/A';
      default:
        return value.toString();
    }
  }

  IconData _getAudioStateIcon(AudioState state) {
    switch (state) {
      case AudioState.stopped:
        return Icons.stop;
      case AudioState.loading:
        return Icons.download;
      case AudioState.buffering:
        return Icons.hourglass_empty;
      case AudioState.playing:
        return Icons.play_arrow;
      case AudioState.paused:
        return Icons.pause;
      case AudioState.error:
        return Icons.error;
    }
  }

  Color _getAudioStateColor(AudioState state) {
    switch (state) {
      case AudioState.stopped:
        return Colors.grey;
      case AudioState.loading:
        return Colors.blue;
      case AudioState.buffering:
        return Colors.orange;
      case AudioState.playing:
        return Colors.green;
      case AudioState.paused:
        return Colors.orange;
      case AudioState.error:
        return Colors.red;
    }
  }

  String _getAudioStateDisplayName(AudioState state) {
    switch (state) {
      case AudioState.stopped:
        return 'Stopped';
      case AudioState.loading:
        return 'Loading';
      case AudioState.buffering:
        return 'Buffering';
      case AudioState.playing:
        return 'Playing';
      case AudioState.paused:
        return 'Paused';
      case AudioState.error:
        return 'Error';
    }
  }
}
