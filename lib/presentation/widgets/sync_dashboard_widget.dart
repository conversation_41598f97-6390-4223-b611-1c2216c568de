import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../core/constants/app_constants.dart';
import '../providers/app_state_provider.dart';

/// Dashboard widget showing real-time sync metrics and charts
class SyncDashboardWidget extends StatelessWidget {
  const SyncDashboardWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Status Card
              _buildStatusCard(context, appState),
              const SizedBox(height: 16),
              
              // Real-time Metrics Cards
              Row(
                children: [
                  Expanded(child: _buildMetricCard(
                    context,
                    'Time Offset',
                    '${appState.currentTimeOffset ?? 0}μs',
                    Icons.access_time,
                    _getOffsetColor(appState.currentTimeOffset),
                  )),
                  const SizedBox(width: 8),
                  Expanded(child: _buildMetricCard(
                    context,
                    'Accuracy',
                    '${((appState.currentSyncMetrics?.accuracy ?? 0) * 100).toStringAsFixed(1)}%',
                    Icons.tablet,
                    _getAccuracyColor(appState.currentSyncMetrics?.accuracy),
                  )),
                ],
              ),
              const SizedBox(height: 8),
              
              Row(
                children: [
                  Expanded(child: _buildMetricCard(
                    context,
                    'Latency',
                    '${appState.currentSyncMetrics?.latency ?? 0}μs',
                    Icons.speed,
                    _getLatencyColor(appState.currentSyncMetrics?.latency),
                  )),
                  const SizedBox(width: 8),
                  Expanded(child: _buildMetricCard(
                    context,
                    'Drift',
                    '${(appState.currentSyncMetrics?.drift ?? 0).toStringAsFixed(3)}',
                    Icons.trending_up,
                    _getDriftColor(appState.currentSyncMetrics?.drift),
                  )),
                ],
              ),
              const SizedBox(height: 16),
              
              // Sync Metrics Chart
              if (appState.syncMetricsHistory.isNotEmpty) ...[
                _buildSyncChart(context, appState),
                const SizedBox(height: 16),
              ],
              
              // Statistics Card
              _buildStatisticsCard(context, appState),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusCard(BuildContext context, AppStateProvider appState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(appState.syncStatus),
                  color: _getStatusColor(appState.syncStatus),
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getStatusTitle(appState.syncStatus),
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        appState.statusMessage,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Device role and connection type
            Row(
              children: [
                Chip(
                  label: Text(appState.deviceRole.toString().split('.').last.toUpperCase()),
                  backgroundColor: _getRoleColor(appState.deviceRole),
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(appState.activeConnectionType.toString().split('.').last.toUpperCase()),
                  backgroundColor: _getConnectionColor(appState.activeConnectionType),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncChart(BuildContext context, AppStateProvider appState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Metrics Over Time',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${value.toInt()}μs',
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < appState.syncMetricsHistory.length) {
                            final time = appState.syncMetricsHistory[index].timestamp;
                            return Text(
                              '${time.minute}:${time.second.toString().padLeft(2, '0')}',
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    // Offset line
                    LineChartBarData(
                      spots: appState.syncMetricsHistory.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.offset.toDouble());
                      }).toList(),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 2,
                      dotData: const FlDotData(show: false),
                    ),
                    // Latency line
                    LineChartBarData(
                      spots: appState.syncMetricsHistory.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.latency.toDouble());
                      }).toList(),
                      isCurved: true,
                      color: Colors.orange,
                      barWidth: 2,
                      dotData: const FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Chart legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem('Offset', Colors.blue),
                const SizedBox(width: 16),
                _buildLegendItem('Latency', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildStatisticsCard(BuildContext context, AppStateProvider appState) {
    final stats = appState.getSyncStatistics();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Statistics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            
            _buildStatRow('Average Offset', '${stats['averageOffset']}μs'),
            _buildStatRow('Average Latency', '${stats['averageLatency']}μs'),
            _buildStatRow('Average Accuracy', '${(stats['averageAccuracy'] * 100).toStringAsFixed(1)}%'),
            _buildStatRow('Max Jitter', '${stats['maxJitter']}μs'),
            _buildStatRow('Sync Count', '${stats['syncCount']}'),
            _buildStatRow('Current Drift', '${stats['currentDrift'].toStringAsFixed(3)}'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  // Helper methods for colors and icons
  IconData _getStatusIcon(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return Icons.signal_wifi_off;
      case SyncStatus.connecting:
        return Icons.sync;
      case SyncStatus.connected:
        return Icons.signal_wifi_4_bar;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.sync_alt;
      case SyncStatus.error:
        return Icons.error;
    }
  }

  Color _getStatusColor(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return Colors.grey;
      case SyncStatus.connecting:
        return Colors.orange;
      case SyncStatus.connected:
        return Colors.blue;
      case SyncStatus.syncing:
        return Colors.orange;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
    }
  }

  String _getStatusTitle(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return 'Disconnected';
      case SyncStatus.connecting:
        return 'Connecting';
      case SyncStatus.connected:
        return 'Connected';
      case SyncStatus.syncing:
        return 'Syncing';
      case SyncStatus.synced:
        return 'Synced';
      case SyncStatus.error:
        return 'Error';
    }
  }

  Color _getRoleColor(DeviceRole role) {
    switch (role) {
      case DeviceRole.master:
        return Colors.blue.shade100;
      case DeviceRole.slave:
        return Colors.green.shade100;
      case DeviceRole.standalone:
        return Colors.grey.shade100;
    }
  }

  Color _getConnectionColor(ConnectionType type) {
    switch (type) {
      case ConnectionType.ble:
        return Colors.blue.shade100;
      case ConnectionType.hotspot:
        return Colors.green.shade100;
      case ConnectionType.webrtc:
        return Colors.orange.shade100;
      case ConnectionType.ultrasonic:
        return Colors.purple.shade100;
      case ConnectionType.bluetoothAudio:
        return Colors.indigo.shade100;
    }
  }

  Color _getOffsetColor(int? offset) {
    if (offset == null) return Colors.grey;
    final absOffset = offset.abs();
    if (absOffset < 10000) return Colors.green; // < 10ms
    if (absOffset < 25000) return Colors.orange; // < 25ms
    return Colors.red; // >= 25ms
  }

  Color _getAccuracyColor(double? accuracy) {
    if (accuracy == null) return Colors.grey;
    if (accuracy > 0.9) return Colors.green; // > 90%
    if (accuracy > 0.7) return Colors.orange; // > 70%
    return Colors.red; // <= 70%
  }

  Color _getLatencyColor(int? latency) {
    if (latency == null) return Colors.grey;
    if (latency < 20000) return Colors.green; // < 20ms
    if (latency < 50000) return Colors.orange; // < 50ms
    return Colors.red; // >= 50ms
  }

  Color _getDriftColor(double? drift) {
    if (drift == null) return Colors.grey;
    final absDrift = drift.abs();
    if (absDrift < 0.001) return Colors.green; // Very stable
    if (absDrift < 0.01) return Colors.orange; // Moderate drift
    return Colors.red; // High drift
  }
}
