import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:logger/logger.dart';
import 'package:fftea/fftea.dart';

import '../core/constants/app_constants.dart';
import 'security_manager.dart';

/// Abstract interface for ultrasonic operations
abstract class IUltrasonicManager {
  Stream<String> get receivedDataStream;
  Stream<double> get signalStrengthStream;
  Future<void> initialize();
  Future<void> startTransmitting();
  Future<void> stopTransmitting();
  Future<void> startListening();
  Future<void> stopListening();
  Future<void> transmitData(String data);
  bool get isTransmitting;
  bool get isListening;
  void dispose();
}

/// Ultrasonic Manager implementation for network-less communication
class UltrasonicManager implements IUltrasonicManager {
  final Logger _logger;
  final SecurityManager _securityManager;

  final FlutterSoundRecorder _recorder = FlutterSoundRecorder();
  final FlutterSoundPlayer _player = FlutterSoundPlayer();

  final StreamController<String> _receivedDataController =
      StreamController<String>.broadcast();
  final StreamController<double> _signalStrengthController =
      StreamController<double>.broadcast();

  Timer? _transmissionTimer;
  StreamSubscription<RecordingDisposition>? _recordingSubscription;

  bool _isTransmitting = false;
  bool _isListening = false;
  bool _isInitialized = false;

  // FSK parameters
  final int _baseFrequency = AppConstants.ultrasonicFrequency;
  final int _frequencyShift = 1000; // Hz shift for FSK
  final int _symbolDuration = 50; // ms per symbol
  final int _sampleRate = AppConstants.sampleRate;

  // Error correction
  final List<int> _hammingMatrix = [
    0x1,
    0x2,
    0x4,
    0x8,
    0x3,
    0x5,
    0x6,
    0x7,
    0x9,
    0xA,
    0xB,
    0xC,
    0xD,
    0xE,
    0xF,
  ];

  // Signal processing
  final FFT _fft = FFT(1024);
  final List<double> _signalBuffer = [];
  final int _bufferSize = 4096;

  UltrasonicManager({
    required Logger logger,
    required SecurityManager securityManager,
  }) : _logger = logger,
       _securityManager = securityManager;

  @override
  Stream<String> get receivedDataStream => _receivedDataController.stream;

  @override
  Stream<double> get signalStrengthStream => _signalStrengthController.stream;

  @override
  bool get isTransmitting => _isTransmitting;

  @override
  bool get isListening => _isListening;

  @override
  Future<void> initialize() async {
    try {
      _logger.i('Initializing Ultrasonic Manager');

      // Initialize recorder
      await _recorder.openRecorder();
      await _recorder.setSubscriptionDuration(const Duration(milliseconds: 10));

      // Initialize player
      await _player.openPlayer();

      _isInitialized = true;
      _logger.i('Ultrasonic Manager initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize Ultrasonic Manager: $e');
      rethrow;
    }
  }

  @override
  Future<void> startTransmitting() async {
    try {
      if (!_isInitialized) {
        throw Exception('Ultrasonic Manager not initialized');
      }

      _logger.i('Starting ultrasonic transmission');
      _isTransmitting = true;
    } catch (e) {
      _logger.e('Failed to start ultrasonic transmission: $e');
      rethrow;
    }
  }

  @override
  Future<void> stopTransmitting() async {
    try {
      _logger.i('Stopping ultrasonic transmission');

      _transmissionTimer?.cancel();
      _transmissionTimer = null;
      _isTransmitting = false;

      await _player.stopPlayer();
    } catch (e) {
      _logger.e('Failed to stop ultrasonic transmission: $e');
    }
  }

  @override
  Future<void> startListening() async {
    try {
      if (!_isInitialized) {
        throw Exception('Ultrasonic Manager not initialized');
      }

      _logger.i('Starting ultrasonic listening');

      // Start recording with stream processing
      _recordingSubscription = _recorder.onProgress?.listen((disposition) {
        _processAudioData(disposition);
      });

      await _recorder.startRecorder(
        codec: Codec.pcm16,
        sampleRate: _sampleRate,
        numChannels: 1,
      );

      _isListening = true;
      _logger.i('Ultrasonic listening started successfully');
    } catch (e) {
      _logger.e('Failed to start ultrasonic listening: $e');
      rethrow;
    }
  }

  @override
  Future<void> stopListening() async {
    try {
      _logger.i('Stopping ultrasonic listening');

      await _recordingSubscription?.cancel();
      _recordingSubscription = null;

      await _recorder.stopRecorder();
      _isListening = false;

      _signalBuffer.clear();
    } catch (e) {
      _logger.e('Failed to stop ultrasonic listening: $e');
    }
  }

  @override
  Future<void> transmitData(String data) async {
    try {
      if (!_isTransmitting) {
        throw Exception('Not in transmitting mode');
      }

      _logger.d('Transmitting ultrasonic data: ${data.length} bytes');

      // Encode data with error correction
      final encodedData = _encodeWithErrorCorrection(data);

      // Generate FSK modulated audio
      final audioData = _generateFSKSignal(encodedData);

      // Play the generated audio
      // Note: flutter_sound API has changed, using startPlayer with buffer
      await _player.startPlayer(
        fromDataBuffer: Uint8List.fromList(audioData),
        codec: Codec.pcm16,
        whenFinished: () {
          _logger.d('Ultrasonic transmission completed');
        },
      );
    } catch (e) {
      _logger.e('Failed to transmit ultrasonic data: $e');
      rethrow;
    }
  }

  /// Encode data with Hamming error correction
  String _encodeWithErrorCorrection(String data) {
    final bytes = utf8.encode(data);
    final encodedBytes = <int>[];

    for (final byte in bytes) {
      // Split byte into two 4-bit nibbles
      final highNibble = (byte >> 4) & 0xF;
      final lowNibble = byte & 0xF;

      // Apply Hamming(7,4) encoding
      encodedBytes.add(_hammingEncode(highNibble));
      encodedBytes.add(_hammingEncode(lowNibble));
    }

    return base64Encode(encodedBytes);
  }

  /// Decode data with Hamming error correction
  String? _decodeWithErrorCorrection(String encodedData) {
    try {
      final encodedBytes = base64Decode(encodedData);
      final decodedBytes = <int>[];

      for (int i = 0; i < encodedBytes.length; i += 2) {
        if (i + 1 >= encodedBytes.length) break;

        // Decode Hamming(7,4) codes
        final highNibble = _hammingDecode(encodedBytes[i]);
        final lowNibble = _hammingDecode(encodedBytes[i + 1]);

        if (highNibble == null || lowNibble == null) {
          _logger.w('Hamming decode error at position $i');
          return null; // Error correction failed
        }

        // Combine nibbles back into byte
        final byte = (highNibble << 4) | lowNibble;
        decodedBytes.add(byte);
      }

      return utf8.decode(decodedBytes);
    } catch (e) {
      _logger.e('Failed to decode with error correction: $e');
      return null;
    }
  }

  /// Hamming(7,4) encoding
  int _hammingEncode(int data) {
    // Generate parity bits for Hamming(7,4) code
    final d1 = (data >> 0) & 1;
    final d2 = (data >> 1) & 1;
    final d3 = (data >> 2) & 1;
    final d4 = (data >> 3) & 1;

    final p1 = d1 ^ d2 ^ d4;
    final p2 = d1 ^ d3 ^ d4;
    final p3 = d2 ^ d3 ^ d4;

    return (p1 << 0) |
        (p2 << 1) |
        (d1 << 2) |
        (p3 << 3) |
        (d2 << 4) |
        (d3 << 5) |
        (d4 << 6);
  }

  /// Hamming(7,4) decoding with error correction
  int? _hammingDecode(int code) {
    // Extract bits
    final p1 = (code >> 0) & 1;
    final p2 = (code >> 1) & 1;
    final d1 = (code >> 2) & 1;
    final p3 = (code >> 3) & 1;
    final d2 = (code >> 4) & 1;
    final d3 = (code >> 5) & 1;
    final d4 = (code >> 6) & 1;

    // Calculate syndrome
    final s1 = p1 ^ d1 ^ d2 ^ d4;
    final s2 = p2 ^ d1 ^ d3 ^ d4;
    final s3 = p3 ^ d2 ^ d3 ^ d4;
    final syndrome = (s3 << 2) | (s2 << 1) | s1;

    if (syndrome == 0) {
      // No error
      return (d4 << 3) | (d3 << 2) | (d2 << 1) | d1;
    } else if (syndrome <= 7) {
      // Single bit error - correct it
      final correctedCode = code ^ (1 << (syndrome - 1));
      return _hammingDecode(correctedCode);
    } else {
      // Multiple bit errors - cannot correct
      return null;
    }
  }

  /// Generate FSK modulated signal
  List<int> _generateFSKSignal(String data) {
    final samples = <int>[];
    final samplesPerSymbol = (_sampleRate * _symbolDuration) ~/ 1000;

    // Add preamble for synchronization
    final preamble = '10101010'; // Alternating pattern
    final fullData = preamble + data;

    for (int i = 0; i < fullData.length; i++) {
      final bit = fullData[i] == '1' ? 1 : 0;
      final frequency = _baseFrequency + (bit * _frequencyShift);

      // Generate sine wave for this symbol
      for (int j = 0; j < samplesPerSymbol; j++) {
        final t = j / _sampleRate;
        final amplitude = 0.8; // 80% amplitude to avoid clipping
        final sample =
            (sin(2 * pi * frequency * t) * amplitude * 32767).round();
        samples.add(sample);
      }
    }

    return samples;
  }

  /// Process incoming audio data for FSK demodulation
  void _processAudioData(RecordingDisposition disposition) {
    try {
      // This is a simplified version - in practice, you'd need to:
      // 1. Buffer audio samples
      // 2. Apply FFT to detect frequencies
      // 3. Demodulate FSK signal
      // 4. Decode bits and apply error correction

      final decibels = disposition.decibels ?? 0.0;
      _signalStrengthController.add(decibels);

      // For demonstration, we'll simulate signal detection
      if (decibels > -30) {
        // Threshold for signal detection
        _simulateSignalDetection();
      }
    } catch (e) {
      _logger.e('Failed to process audio data: $e');
    }
  }

  /// Simulate signal detection and decoding (placeholder)
  void _simulateSignalDetection() {
    // In a real implementation, this would:
    // 1. Use FFT to analyze frequency content
    // 2. Detect FSK frequencies
    // 3. Demodulate to bits
    // 4. Apply error correction
    // 5. Decode to original data

    // For now, we'll just simulate receiving a test message
    Timer(const Duration(milliseconds: 100), () {
      final testData = 'Test ultrasonic message';
      _receivedDataController.add(testData);
    });
  }

  /// Apply FFT analysis to detect frequencies
  List<double> _analyzeFrequencies(List<double> samples) {
    if (samples.length != _fft.size) {
      // Pad or truncate to FFT size
      final paddedSamples = List<double>.filled(_fft.size, 0.0);
      final copyLength = min(samples.length, _fft.size);
      for (int i = 0; i < copyLength; i++) {
        paddedSamples[i] = samples[i];
      }
      samples = paddedSamples;
    }

    // Apply window function (Hamming window)
    for (int i = 0; i < samples.length; i++) {
      final window = 0.54 - 0.46 * cos(2 * pi * i / (samples.length - 1));
      samples[i] *= window;
    }

    // Perform FFT
    final fftResult = _fft.realFft(samples);

    // Calculate magnitude spectrum
    final magnitudes = <double>[];
    for (int i = 0; i < fftResult.length ~/ 2; i++) {
      final real = fftResult[i * 2].x;
      final imag = fftResult[i * 2 + 1].x;
      magnitudes.add(sqrt(real * real + imag * imag));
    }

    return magnitudes;
  }

  /// Get frequency bin for a given frequency
  int _getFrequencyBin(int frequency) {
    return (frequency * _fft.size) ~/ _sampleRate;
  }

  /// Detect if target frequencies are present
  bool _detectFSKFrequencies(List<double> magnitudes) {
    final freq0Bin = _getFrequencyBin(_baseFrequency);
    final freq1Bin = _getFrequencyBin(_baseFrequency + _frequencyShift);

    final threshold = magnitudes.reduce(max) * 0.1; // 10% of max magnitude

    return magnitudes[freq0Bin] > threshold || magnitudes[freq1Bin] > threshold;
  }

  /// Get ultrasonic transmission statistics
  Map<String, dynamic> getTransmissionStats() {
    return {
      'isTransmitting': _isTransmitting,
      'isListening': _isListening,
      'baseFrequency': _baseFrequency,
      'frequencyShift': _frequencyShift,
      'symbolDuration': _symbolDuration,
      'sampleRate': _sampleRate,
      'bufferSize': _signalBuffer.length,
    };
  }

  @override
  void dispose() {
    _logger.i('Disposing Ultrasonic Manager');
    stopTransmitting();
    stopListening();
    _recorder.closeRecorder();
    _player.closePlayer();
    _receivedDataController.close();
    _signalStrengthController.close();
  }
}
