import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:udp/udp.dart';

import '../core/constants/app_constants.dart';
import '../data/models/sync_packet.dart';
import '../data/repositories/settings_repository.dart';
import 'security_manager.dart';

/// Abstract interface for synchronization operations
abstract class ISyncManager {
  Stream<SyncMetrics> get syncMetricsStream;
  Stream<int> get timeOffsetStream;
  Future<void> startMasterSync();
  Future<void> startSlaveSync();
  Future<void> stopSync();
  void sendSyncPacket(SyncPacket packet);
  int? get currentTimeOffset;
  double get currentDrift;
  void dispose();
}

/// Sync Manager implementation for clock synchronization
class SyncManager implements ISyncManager {
  final Logger _logger;
  final SecurityManager _securityManager;
  final SettingsRepository _settingsRepository;

  final StreamController<SyncMetrics> _syncMetricsController =
      StreamController<SyncMetrics>.broadcast();
  final StreamController<int> _timeOffsetController =
      StreamController<int>.broadcast();

  UDP? _udpSocket;
  Timer? _syncTimer;
  Timer? _cleanupTimer;

  final List<Map<String, dynamic>> _jitterBuffer = [];
  final List<SyncMetrics> _metricsHistory = [];

  int _sequenceId = 0;
  int? _timeOffset;
  double _drift = 0.0;
  double _driftRate = 0.0;
  bool _isMaster = false;
  bool _isRunning = false;

  // Kalman filter variables for better time estimation
  double _kalmanGain = 0.1;
  double _processNoise = 1e-6;
  double _measurementNoise = 1e-3;
  double _estimationError = 1.0;

  SyncManager({
    required Logger logger,
    required SecurityManager securityManager,
    required SettingsRepository settingsRepository,
  }) : _logger = logger,
       _securityManager = securityManager,
       _settingsRepository = settingsRepository;

  @override
  Stream<SyncMetrics> get syncMetricsStream => _syncMetricsController.stream;

  @override
  Stream<int> get timeOffsetStream => _timeOffsetController.stream;

  @override
  int? get currentTimeOffset => _timeOffset;

  @override
  double get currentDrift => _drift;

  @override
  Future<void> startMasterSync() async {
    try {
      _logger.i('Starting master synchronization');

      if (_isRunning) {
        await stopSync();
      }

      _isMaster = true;
      _isRunning = true;

      // Initialize UDP socket
      _udpSocket = await UDP.bind(
        Endpoint.any(port: Port(AppConstants.udpPort)),
      );
      _udpSocket?.asStream().listen(_handleIncomingPacket);

      // Start periodic sync broadcast
      final syncInterval = Duration(
        milliseconds: _settingsRepository.syncInterval,
      );
      _syncTimer = Timer.periodic(syncInterval, (timer) {
        _broadcastSyncPacket();
      });

      // Start cleanup timer for old metrics
      _cleanupTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
        _cleanupOldMetrics();
      });

      _logger.i('Master synchronization started successfully');
    } catch (e) {
      _logger.e('Failed to start master sync: $e');
      rethrow;
    }
  }

  @override
  Future<void> startSlaveSync() async {
    try {
      _logger.i('Starting slave synchronization');

      if (_isRunning) {
        await stopSync();
      }

      _isMaster = false;
      _isRunning = true;

      // Initialize UDP socket
      _udpSocket = await UDP.bind(
        Endpoint.any(port: Port(AppConstants.udpPort)),
      );
      _udpSocket?.asStream().listen(_handleIncomingPacket);

      // Start cleanup timer
      _cleanupTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
        _cleanupOldMetrics();
        _removeStaleJitterBufferEntries();
      });

      _logger.i('Slave synchronization started successfully');
    } catch (e) {
      _logger.e('Failed to start slave sync: $e');
      rethrow;
    }
  }

  @override
  Future<void> stopSync() async {
    try {
      _logger.i('Stopping synchronization');

      _isRunning = false;
      _syncTimer?.cancel();
      _cleanupTimer?.cancel();

      _udpSocket?.close();
      _udpSocket = null;

      _jitterBuffer.clear();
      _timeOffset = null;
      _drift = 0.0;
      _driftRate = 0.0;

      _logger.i('Synchronization stopped');
    } catch (e) {
      _logger.e('Failed to stop sync: $e');
    }
  }

  @override
  void sendSyncPacket(SyncPacket packet) {
    if (!_isRunning || _udpSocket == null) return;

    try {
      final encryptedData = _securityManager.encryptSyncPacket(packet.toJson());
      _udpSocket?.send(
        utf8.encode(encryptedData),
        Endpoint.broadcast(port: Port(AppConstants.udpPort)),
      );

      _logger.d('Sent sync packet: ${packet.sequenceId}');
    } catch (e) {
      _logger.e('Failed to send sync packet: $e');
    }
  }

  void _broadcastSyncPacket() {
    if (!_isMaster || !_isRunning) return;

    final packet = SyncPacket(
      masterTime: DateTime.now().microsecondsSinceEpoch,
      sequenceId: _sequenceId++,
      deviceId: 'master',
      timestamp: DateTime.now().microsecondsSinceEpoch,
    );

    sendSyncPacket(packet);
  }

  void _handleIncomingPacket(Datagram datagram) {
    if (_isMaster) return; // Masters don't process sync packets

    try {
      final encryptedData = utf8.decode(datagram.data);
      final packetData = _securityManager.decryptSyncPacket(encryptedData);

      if (packetData == null) {
        _logger.w('Failed to decrypt sync packet');
        return;
      }

      final packet = SyncPacket.fromJson(packetData);
      _processSyncPacket(packet);
    } catch (e) {
      _logger.e('Failed to handle incoming packet: $e');
    }
  }

  void _processSyncPacket(SyncPacket packet) {
    final receiveTime = DateTime.now().microsecondsSinceEpoch;
    final transmitTime = packet.masterTime;

    // Calculate round-trip time and offset
    final rtt = receiveTime - transmitTime;
    final offset = rtt ~/ 2; // Simple offset calculation

    // Add to jitter buffer
    _jitterBuffer.add({
      'offset': offset,
      'timestamp': receiveTime,
      'sequence': packet.sequenceId,
      'rtt': rtt,
    });

    // Maintain buffer size
    final maxBufferSize = _settingsRepository.jitterBufferSize;
    while (_jitterBuffer.length > maxBufferSize) {
      _jitterBuffer.removeAt(0);
    }

    // Calculate stable offset using Kalman filter
    if (_jitterBuffer.length >= AppConstants.minJitterBufferSize) {
      _updateTimeOffset();
      _calculateDrift();
      _generateMetrics();
    }
  }

  void _updateTimeOffset() {
    if (_jitterBuffer.isEmpty) return;

    // Use Kalman filter for better time estimation
    final measurement = _calculateMedianOffset().toDouble();

    // Prediction step
    final predictedOffset =
        (_timeOffset?.toDouble() ?? measurement) + _driftRate;
    final predictedError = _estimationError + _processNoise;

    // Update step
    _kalmanGain = predictedError / (predictedError + _measurementNoise);
    final newOffset =
        predictedOffset + _kalmanGain * (measurement - predictedOffset);
    _estimationError = (1 - _kalmanGain) * predictedError;

    _timeOffset = newOffset.round();
    _timeOffsetController.add(_timeOffset!);

    _logger.d('Updated time offset: $_timeOffset μs');
  }

  int _calculateMedianOffset() {
    final offsets =
        _jitterBuffer.map((entry) => entry['offset'] as int).toList();
    offsets.sort();

    final middle = offsets.length ~/ 2;
    if (offsets.length % 2 == 0) {
      return (offsets[middle - 1] + offsets[middle]) ~/ 2;
    } else {
      return offsets[middle];
    }
  }

  void _calculateDrift() {
    if (_jitterBuffer.length < 5) return;

    // Linear regression for drift calculation
    final n = _jitterBuffer.length;
    double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    for (int i = 0; i < n; i++) {
      final x = i.toDouble();
      final y = (_jitterBuffer[i]['offset'] as int).toDouble();
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumXX += x * x;
    }

    final slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    _driftRate = slope;

    // Apply exponential moving average for smoothing
    _drift =
        AppConstants.driftCorrectionAlpha * slope +
        (1 - AppConstants.driftCorrectionAlpha) * _drift;
  }

  void _generateMetrics() {
    if (_jitterBuffer.isEmpty || _timeOffset == null) return;

    final latestEntry = _jitterBuffer.last;
    final latency = latestEntry['rtt'] as int;

    // Calculate accuracy based on jitter
    final offsets = _jitterBuffer.map((e) => e['offset'] as int).toList();
    final mean = offsets.reduce((a, b) => a + b) / offsets.length;
    final variance =
        offsets.map((o) => pow(o - mean, 2)).reduce((a, b) => a + b) /
        offsets.length;
    final jitter = sqrt(variance);
    final accuracy = max(0.0, 1.0 - (jitter / AppConstants.maxSyncOffsetUs));

    final metrics = SyncMetrics(
      offset: _timeOffset!,
      latency: latency,
      drift: _drift,
      jitterBufferSize: _jitterBuffer.length,
      timestamp: DateTime.now(),
      accuracy: accuracy,
    );

    _metricsHistory.add(metrics);
    _syncMetricsController.add(metrics);
  }

  void _removeStaleJitterBufferEntries() {
    final now = DateTime.now().microsecondsSinceEpoch;
    _jitterBuffer.removeWhere((entry) {
      final timestamp = entry['timestamp'] as int;
      return now - timestamp > 10000000; // Remove entries older than 10 seconds
    });
  }

  void _cleanupOldMetrics() {
    final cutoff = DateTime.now().subtract(const Duration(minutes: 5));
    _metricsHistory.removeWhere(
      (metrics) => metrics.timestamp.isBefore(cutoff),
    );
  }

  /// Get recent sync metrics for charting
  List<SyncMetrics> getRecentMetrics({int? maxCount}) {
    final count = maxCount ?? AppConstants.chartDataPoints;
    if (_metricsHistory.length <= count) {
      return List.from(_metricsHistory);
    }
    return _metricsHistory.sublist(_metricsHistory.length - count);
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStatistics() {
    if (_metricsHistory.isEmpty) {
      return {
        'averageOffset': 0,
        'averageLatency': 0,
        'averageAccuracy': 0.0,
        'maxJitter': 0,
        'syncCount': 0,
      };
    }

    final offsets = _metricsHistory.map((m) => m.offset).toList();
    final latencies = _metricsHistory.map((m) => m.latency).toList();
    final accuracies = _metricsHistory.map((m) => m.accuracy).toList();

    final avgOffset = offsets.reduce((a, b) => a + b) / offsets.length;
    final avgLatency = latencies.reduce((a, b) => a + b) / latencies.length;
    final avgAccuracy = accuracies.reduce((a, b) => a + b) / accuracies.length;

    final maxOffset = offsets.reduce(max);
    final minOffset = offsets.reduce(min);
    final maxJitter = maxOffset - minOffset;

    return {
      'averageOffset': avgOffset.round(),
      'averageLatency': avgLatency.round(),
      'averageAccuracy': avgAccuracy,
      'maxJitter': maxJitter,
      'syncCount': _metricsHistory.length,
      'currentDrift': _drift,
    };
  }

  @override
  void dispose() {
    _logger.i('Disposing Sync Manager');
    stopSync();
    _syncMetricsController.close();
    _timeOffsetController.close();
  }
}
