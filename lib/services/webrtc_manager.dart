import 'dart:async';
import 'dart:convert';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_constants.dart';
import 'security_manager.dart';

/// Abstract interface for WebRTC operations
abstract class IWebRTCManager {
  Stream<RTCPeerConnectionState> get connectionStateStream;
  Stream<String> get dataChannelMessageStream;
  Future<void> initialize();
  Future<void> createOffer();
  Future<void> createAnswer(String offer);
  Future<void> setRemoteDescription(String sdp, String type);
  Future<void> addIceCandidate(
    String candidate,
    String sdpMid,
    int sdpMLineIndex,
  );
  Future<void> sendData(String data);
  Future<void> close();
  bool get isConnected;
  void dispose();
}

/// WebRTC Manager implementation for P2P communication
class WebRTCManager implements IWebRTCManager {
  final Logger _logger;
  final SecurityManager _securityManager;

  RTCPeerConnection? _peerConnection;
  RTCDataChannel? _dataChannel;

  final StreamController<RTCPeerConnectionState> _connectionStateController =
      StreamController<RTCPeerConnectionState>.broadcast();
  final StreamController<String> _dataChannelMessageController =
      StreamController<String>.broadcast();

  final List<RTCIceCandidate> _iceCandidates = [];
  bool _isInitialized = false;
  bool _isConnected = false;

  WebRTCManager({
    required Logger logger,
    required SecurityManager securityManager,
  }) : _logger = logger,
       _securityManager = securityManager;

  @override
  Stream<RTCPeerConnectionState> get connectionStateStream =>
      _connectionStateController.stream;

  @override
  Stream<String> get dataChannelMessageStream =>
      _dataChannelMessageController.stream;

  @override
  bool get isConnected => _isConnected;

  @override
  Future<void> initialize() async {
    try {
      _logger.i('Initializing WebRTC');

      // WebRTC configuration with STUN servers
      final configuration = {
        'iceServers': [
          {'urls': 'stun:stun.l.google.com:19302'},
          {'urls': 'stun:stun1.l.google.com:19302'},
          {'urls': 'stun:stun2.l.google.com:19302'},
        ],
        'iceCandidatePoolSize': 10,
      };

      // Create peer connection
      _peerConnection = await createPeerConnection(configuration);

      // Set up connection state listener
      _peerConnection?.onConnectionState = (state) {
        _logger.i('WebRTC connection state: $state');
        _connectionStateController.add(state);
        _isConnected =
            state == RTCPeerConnectionState.RTCPeerConnectionStateConnected;
      };

      // Set up ICE candidate listener
      _peerConnection?.onIceCandidate = (candidate) {
        _logger.d('ICE candidate generated: ${candidate.candidate}');
        _iceCandidates.add(candidate);
        // In a real implementation, you would send this to the remote peer
        // via your signaling mechanism (BLE, WebSocket, etc.)
      };

      // Set up data channel for master (offer side)
      _dataChannel = await _peerConnection?.createDataChannel(
        'sync',
        RTCDataChannelInit()
          ..ordered = true
          ..maxRetransmits = 3,
      );

      _setupDataChannel(_dataChannel!);

      // Set up data channel listener for slave (answer side)
      _peerConnection?.onDataChannel = (channel) {
        _logger.i('Data channel received: ${channel.label}');
        _setupDataChannel(channel);
      };

      _isInitialized = true;
      _logger.i('WebRTC initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize WebRTC: $e');
      rethrow;
    }
  }

  @override
  Future<void> createOffer() async {
    try {
      if (!_isInitialized || _peerConnection == null) {
        throw Exception('WebRTC not initialized');
      }

      _logger.i('Creating WebRTC offer');

      // Create offer
      final offer = await _peerConnection!.createOffer();
      await _peerConnection!.setLocalDescription(offer);

      _logger.i('WebRTC offer created: ${offer.sdp?.substring(0, 100)}...');

      // In a real implementation, you would send this offer to the remote peer
      // via your signaling mechanism
    } catch (e) {
      _logger.e('Failed to create WebRTC offer: $e');
      rethrow;
    }
  }

  @override
  Future<void> createAnswer(String offer) async {
    try {
      if (!_isInitialized || _peerConnection == null) {
        throw Exception('WebRTC not initialized');
      }

      _logger.i('Creating WebRTC answer');

      // Set remote description (offer)
      await _peerConnection!.setRemoteDescription(
        RTCSessionDescription(offer, 'offer'),
      );

      // Create answer
      final answer = await _peerConnection!.createAnswer();
      await _peerConnection!.setLocalDescription(answer);

      _logger.i('WebRTC answer created: ${answer.sdp?.substring(0, 100)}...');

      // In a real implementation, you would send this answer back to the offerer
    } catch (e) {
      _logger.e('Failed to create WebRTC answer: $e');
      rethrow;
    }
  }

  @override
  Future<void> setRemoteDescription(String sdp, String type) async {
    try {
      if (!_isInitialized || _peerConnection == null) {
        throw Exception('WebRTC not initialized');
      }

      _logger.i('Setting remote description: $type');

      await _peerConnection!.setRemoteDescription(
        RTCSessionDescription(sdp, type),
      );

      _logger.i('Remote description set successfully');
    } catch (e) {
      _logger.e('Failed to set remote description: $e');
      rethrow;
    }
  }

  @override
  Future<void> addIceCandidate(
    String candidate,
    String sdpMid,
    int sdpMLineIndex,
  ) async {
    try {
      if (!_isInitialized || _peerConnection == null) {
        throw Exception('WebRTC not initialized');
      }

      _logger.d('Adding ICE candidate: $candidate');

      final iceCandidate = RTCIceCandidate(candidate, sdpMid, sdpMLineIndex);
      await _peerConnection!.addCandidate(iceCandidate);

      _logger.d('ICE candidate added successfully');
    } catch (e) {
      _logger.e('Failed to add ICE candidate: $e');
      rethrow;
    }
  }

  @override
  Future<void> sendData(String data) async {
    try {
      if (_dataChannel == null ||
          _dataChannel!.state != RTCDataChannelState.RTCDataChannelOpen) {
        throw Exception('Data channel not available');
      }

      // Encrypt data before sending
      final encryptedData = await _securityManager.encryptData(data);

      await _dataChannel!.send(RTCDataChannelMessage(encryptedData));
      _logger.d('Sent data via WebRTC: ${data.length} bytes');
    } catch (e) {
      _logger.e('Failed to send data via WebRTC: $e');
      rethrow;
    }
  }

  @override
  Future<void> close() async {
    try {
      _logger.i('Closing WebRTC connection');

      await _dataChannel?.close();
      await _peerConnection?.close();

      _dataChannel = null;
      _peerConnection = null;
      _isInitialized = false;
      _isConnected = false;
      _iceCandidates.clear();

      _logger.i('WebRTC connection closed');
    } catch (e) {
      _logger.e('Failed to close WebRTC connection: $e');
    }
  }

  void _setupDataChannel(RTCDataChannel channel) {
    channel.onMessage = (message) {
      try {
        // Decrypt received data
        final decryptedData = _securityManager.decryptData(message.text);
        _dataChannelMessageController.add(decryptedData);
        _logger.d('Received data via WebRTC: ${decryptedData.length} bytes');
      } catch (e) {
        _logger.e('Failed to decrypt WebRTC message: $e');
      }
    };

    channel.onDataChannelState = (state) {
      _logger.i('Data channel state: $state');
    };
  }

  /// Get local SDP for signaling
  Future<String?> getLocalSDP() async {
    final description = await _peerConnection?.getLocalDescription();
    return description?.sdp;
  }

  /// Get local SDP type for signaling
  Future<String?> getLocalSDPType() async {
    final description = await _peerConnection?.getLocalDescription();
    return description?.type;
  }

  /// Get collected ICE candidates for signaling
  List<Map<String, dynamic>> getIceCandidates() {
    return _iceCandidates
        .map(
          (candidate) => {
            'candidate': candidate.candidate,
            'sdpMid': candidate.sdpMid,
            'sdpMLineIndex': candidate.sdpMLineIndex,
          },
        )
        .toList();
  }

  /// Send sync packet via WebRTC data channel
  Future<void> sendSyncPacket(Map<String, dynamic> packet) async {
    try {
      final jsonData = jsonEncode(packet);
      await sendData(jsonData);
    } catch (e) {
      _logger.e('Failed to send sync packet via WebRTC: $e');
      rethrow;
    }
  }

  /// Get connection statistics
  Future<Map<String, dynamic>> getConnectionStats() async {
    try {
      if (_peerConnection == null) {
        return {'error': 'No peer connection'};
      }

      final stats = await _peerConnection!.getStats();
      final statsMap = <String, dynamic>{};

      for (final report in stats) {
        statsMap[report.id] = {
          'type': report.type,
          'timestamp': report.timestamp,
          'values': report.values,
        };
      }

      return statsMap;
    } catch (e) {
      _logger.e('Failed to get connection stats: $e');
      return {'error': e.toString()};
    }
  }

  /// Check if data channel is ready for sending
  bool get canSendData =>
      _dataChannel != null &&
      _dataChannel!.state == RTCDataChannelState.RTCDataChannelOpen;

  /// Get current connection state
  RTCPeerConnectionState? get connectionState =>
      _peerConnection?.connectionState;

  /// Restart ICE connection
  Future<void> restartIce() async {
    try {
      if (_peerConnection == null) return;

      _logger.i('Restarting ICE connection');
      await _peerConnection!.restartIce();
    } catch (e) {
      _logger.e('Failed to restart ICE: $e');
    }
  }

  @override
  void dispose() {
    _logger.i('Disposing WebRTC Manager');
    close();
    _connectionStateController.close();
    _dataChannelMessageController.close();
  }
}
