import 'dart:async';
import 'dart:convert';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_constants.dart';
import '../data/models/device_info.dart';
import 'security_manager.dart';

/// Abstract interface for BLE operations
abstract class IBLEManager {
  Stream<List<DeviceInfo>> get discoveredDevicesStream;
  Stream<DeviceInfo> get connectionStatusStream;
  Future<void> startAdvertising();
  Future<void> startScanning();
  Future<void> stopScanning();
  Future<void> connectToDevice(String deviceId);
  Future<void> disconnect();
  Future<void> sendData(String deviceId, String data);
  void dispose();
}

/// BLE Manager implementation for device discovery and communication
class BLEManager implements IBLEManager {
  final Logger _logger;
  final SecurityManager _securityManager;

  final StreamController<List<DeviceInfo>> _discoveredDevicesController =
      StreamController<List<DeviceInfo>>.broadcast();
  final StreamController<DeviceInfo> _connectionStatusController =
      StreamController<DeviceInfo>.broadcast();

  final Map<String, DeviceInfo> _discoveredDevices = {};
  final Map<String, BluetoothDevice> _connectedDevices = {};
  final Map<String, BluetoothCharacteristic> _characteristics = {};

  StreamSubscription<List<ScanResult>>? _scanSubscription;
  Timer? _advertisingTimer;
  bool _isScanning = false;
  bool _isAdvertising = false;

  BLEManager({required Logger logger, required SecurityManager securityManager})
    : _logger = logger,
      _securityManager = securityManager;

  @override
  Stream<List<DeviceInfo>> get discoveredDevicesStream =>
      _discoveredDevicesController.stream;

  @override
  Stream<DeviceInfo> get connectionStatusStream =>
      _connectionStatusController.stream;

  @override
  Future<void> startAdvertising() async {
    try {
      _logger.i('Starting BLE advertising as master');

      // Check if Bluetooth is available
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception(AppConstants.bleNotAvailable);
      }

      // Check if Bluetooth is on
      if (await FlutterBluePlus.adapterState.first !=
          BluetoothAdapterState.on) {
        throw Exception('Bluetooth is not enabled');
      }

      // Note: flutter_blue_plus doesn't support BLE advertising directly
      // For now, we'll simulate advertising by being discoverable
      _logger.w('BLE advertising not directly supported by flutter_blue_plus');
      _logger.i('Using connection-based approach instead');

      _isAdvertising = true;
      _logger.i('BLE advertising mode enabled (connection-based)');

      // Start scanning to make device discoverable to others
      await startScanning();
    } catch (e) {
      _logger.e('Failed to start BLE advertising: $e');
      rethrow;
    }
  }

  @override
  Future<void> startScanning() async {
    try {
      _logger.i('Starting BLE scanning for devices');

      if (_isScanning) {
        await stopScanning();
      }

      // Check if Bluetooth is available
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception(AppConstants.bleNotAvailable);
      }

      // Start scanning
      _scanSubscription = FlutterBluePlus.scanResults.listen(
        _handleScanResults,
        onError: (error) {
          _logger.e('BLE scan error: $error');
        },
      );

      await FlutterBluePlus.startScan(
        withServices: [Guid(AppConstants.serviceUuid)],
        timeout: const Duration(seconds: 30),
      );

      _isScanning = true;
      _logger.i('BLE scanning started successfully');
    } catch (e) {
      _logger.e('Failed to start BLE scanning: $e');
      rethrow;
    }
  }

  @override
  Future<void> stopScanning() async {
    try {
      if (_isScanning) {
        await FlutterBluePlus.stopScan();
        await _scanSubscription?.cancel();
        _scanSubscription = null;
        _isScanning = false;
        _logger.i('BLE scanning stopped');
      }
    } catch (e) {
      _logger.e('Failed to stop BLE scanning: $e');
    }
  }

  @override
  Future<void> connectToDevice(String deviceId) async {
    try {
      _logger.i('Connecting to device: $deviceId');

      final deviceInfo = _discoveredDevices[deviceId];
      if (deviceInfo == null) {
        throw Exception('Device not found: $deviceId');
      }

      // Find the Bluetooth device
      final scanResults = await FlutterBluePlus.scanResults.first;
      BluetoothDevice? bluetoothDevice;

      for (final result in scanResults) {
        if (result.device.remoteId.toString() == deviceId) {
          bluetoothDevice = result.device;
          break;
        }
      }

      if (bluetoothDevice == null) {
        throw Exception('Bluetooth device not found: $deviceId');
      }

      // Connect to device
      await bluetoothDevice.connect(timeout: const Duration(seconds: 10));
      _connectedDevices[deviceId] = bluetoothDevice;

      // Discover services
      final services = await bluetoothDevice.discoverServices();
      for (final service in services) {
        if (service.uuid.toString().toUpperCase() ==
            AppConstants.serviceUuid.toUpperCase()) {
          for (final characteristic in service.characteristics) {
            if (characteristic.uuid.toString().toUpperCase() ==
                AppConstants.characteristicUuid.toUpperCase()) {
              _characteristics[deviceId] = characteristic;

              // Enable notifications
              await characteristic.setNotifyValue(true);
              characteristic.lastValueStream.listen((value) {
                _handleCharacteristicData(deviceId, value);
              });

              break;
            }
          }
        }
      }

      // Update device status
      final updatedDevice = deviceInfo.copyWith(
        syncStatus: SyncStatus.connected,
        lastSeen: DateTime.now(),
      );
      _discoveredDevices[deviceId] = updatedDevice;
      _connectionStatusController.add(updatedDevice);

      _logger.i('Successfully connected to device: $deviceId');
    } catch (e) {
      _logger.e('Failed to connect to device $deviceId: $e');

      // Update device status to error
      final deviceInfo = _discoveredDevices[deviceId];
      if (deviceInfo != null) {
        final updatedDevice = deviceInfo.copyWith(
          syncStatus: SyncStatus.error,
          lastSeen: DateTime.now(),
        );
        _discoveredDevices[deviceId] = updatedDevice;
        _connectionStatusController.add(updatedDevice);
      }

      rethrow;
    }
  }

  @override
  Future<void> disconnect() async {
    try {
      _logger.i('Disconnecting all BLE devices');

      for (final device in _connectedDevices.values) {
        await device.disconnect();
      }

      _connectedDevices.clear();
      _characteristics.clear();

      if (_isAdvertising) {
        // Stop scanning instead of advertising
        await FlutterBluePlus.stopScan();
        _advertisingTimer?.cancel();
        _isAdvertising = false;
      }

      await stopScanning();

      _logger.i('All BLE devices disconnected');
    } catch (e) {
      _logger.e('Failed to disconnect BLE devices: $e');
    }
  }

  @override
  Future<void> sendData(String deviceId, String data) async {
    try {
      final characteristic = _characteristics[deviceId];
      if (characteristic == null) {
        throw Exception('No characteristic found for device: $deviceId');
      }

      // Encrypt data if security is enabled
      final encryptedData = await _securityManager.encryptData(data);
      final bytes = utf8.encode(encryptedData);

      await characteristic.write(bytes, withoutResponse: false);
      _logger.d('Sent data to device $deviceId: ${data.length} bytes');
    } catch (e) {
      _logger.e('Failed to send data to device $deviceId: $e');
      rethrow;
    }
  }

  void _handleScanResults(List<ScanResult> results) {
    for (final result in results) {
      final device = result.device;
      final deviceId = device.remoteId.toString();

      // Skip if device name doesn't match
      if (device.platformName != AppConstants.deviceName &&
          device.platformName != AppConstants.slaveDeviceName) {
        continue;
      }

      final deviceInfo = DeviceInfo(
        id: deviceId,
        name: device.platformName,
        role:
            device.platformName == AppConstants.deviceName
                ? DeviceRole.master
                : DeviceRole.slave,
        connectionType: ConnectionType.ble,
        syncStatus: SyncStatus.disconnected,
        lastSeen: DateTime.now(),
        signalStrength: result.rssi,
      );

      _discoveredDevices[deviceId] = deviceInfo;
    }

    _discoveredDevicesController.add(_discoveredDevices.values.toList());
  }

  void _handleCharacteristicData(String deviceId, List<int> data) {
    try {
      final encryptedData = utf8.decode(data);
      final decryptedData = _securityManager.decryptData(encryptedData);
      _logger.d('Received data from device $deviceId: $decryptedData');

      // Handle received data (could be sync packets, commands, etc.)
      // This would typically be forwarded to other managers
    } catch (e) {
      _logger.e('Failed to handle characteristic data from $deviceId: $e');
    }
  }

  @override
  void dispose() {
    _logger.i('Disposing BLE Manager');
    disconnect();
    _discoveredDevicesController.close();
    _connectionStatusController.close();
    _advertisingTimer?.cancel();
  }
}
